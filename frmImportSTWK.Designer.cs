﻿
namespace HIH.CRM.Import
{
    partial class frmImportSTWK
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmImportSTWK));
            this.barManager = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.btnImportExcel = new DevExpress.XtraBars.BarButtonItem();
            this.btnSave = new DevExpress.XtraBars.BarButtonItem();
            this.btnReturn = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.btnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.btnExport = new DevExpress.XtraBars.BarButtonItem();
            this.groupControlImported = new DevExpress.XtraEditors.GroupControl();
            this.gd = new DevExpress.XtraGrid.GridControl();
            this.gdv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.Handler = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Order = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Row = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Supplier = new DevExpress.XtraGrid.Columns.GridColumn();
            this.SupplName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ItemID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ItemName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.QtyOrd = new DevExpress.XtraGrid.Columns.GridColumn();
            this.QtyLeft = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Unit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Status = new DevExpress.XtraGrid.Columns.GridColumn();
            this.DelDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ExtDelDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.POTransporttime = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.bs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlImported)).BeginInit();
            this.groupControlImported.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gd)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager
            // 
            this.barManager.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager.DockControls.Add(this.barDockControlTop);
            this.barManager.DockControls.Add(this.barDockControlBottom);
            this.barManager.DockControls.Add(this.barDockControlLeft);
            this.barManager.DockControls.Add(this.barDockControlRight);
            this.barManager.Form = this;
            this.barManager.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnImportExcel,
            this.btnRefresh,
            this.btnSave,
            this.btnExport,
            this.btnReturn});
            this.barManager.MaxItemId = 6;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnImportExcel),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnReturn, true)});
            this.bar1.Text = "Tools";
            // 
            // btnImportExcel
            // 
            this.btnImportExcel.Caption = "导入Excel";
            this.btnImportExcel.Id = 0;
            this.btnImportExcel.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnImportExcel.ImageOptions.Image")));
            this.btnImportExcel.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnImportExcel.ImageOptions.LargeImage")));
            this.btnImportExcel.Name = "btnImportExcel";
            this.btnImportExcel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnImportExcel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnImportExcel_ItemClick);
            // 
            // btnSave
            // 
            this.btnSave.Caption = "保存";
            this.btnSave.Id = 2;
            this.btnSave.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSave.ImageOptions.Image")));
            this.btnSave.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSave.ImageOptions.LargeImage")));
            this.btnSave.Name = "btnSave";
            this.btnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSave_ItemClick);
            // 
            // btnReturn
            // 
            this.btnReturn.Caption = "返回";
            this.btnReturn.Id = 5;
            this.btnReturn.Name = "btnReturn";
            this.btnReturn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnReturn_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barManager;
            this.barDockControlTop.Size = new System.Drawing.Size(1176, 24);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 578);
            this.barDockControlBottom.Manager = this.barManager;
            this.barDockControlBottom.Size = new System.Drawing.Size(1176, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 24);
            this.barDockControlLeft.Manager = this.barManager;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 554);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1176, 24);
            this.barDockControlRight.Manager = this.barManager;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 554);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Caption = "刷新";
            this.btnRefresh.Id = 1;
            this.btnRefresh.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnRefresh.ImageOptions.Image")));
            this.btnRefresh.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnRefresh.ImageOptions.LargeImage")));
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRefresh_ItemClick);
            // 
            // btnExport
            // 
            this.btnExport.Caption = "导出";
            this.btnExport.Id = 4;
            this.btnExport.Name = "btnExport";
            // 
            // groupControlImported
            // 
            this.groupControlImported.Controls.Add(this.gd);
            this.groupControlImported.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControlImported.Location = new System.Drawing.Point(0, 24);
            this.groupControlImported.Name = "groupControlImported";
            this.groupControlImported.Size = new System.Drawing.Size(1176, 554);
            this.groupControlImported.TabIndex = 14;
            this.groupControlImported.Text = "Excel导入列表";
            // 
            // gd
            // 
            this.gd.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gd.Location = new System.Drawing.Point(2, 23);
            this.gd.MainView = this.gdv;
            this.gd.MenuManager = this.barManager;
            this.gd.Name = "gd";
            this.gd.Size = new System.Drawing.Size(1172, 529);
            this.gd.TabIndex = 0;
            this.gd.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gdv});
            // 
            // gdv
            // 
            this.gdv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.Handler,
            this.Order,
            this.Row,
            this.Supplier,
            this.SupplName,
            this.ItemID,
            this.ItemName,
            this.QtyOrd,
            this.QtyLeft,
            this.Unit,
            this.Status,
            this.DelDate,
            this.ExtDelDate,
            this.POTransporttime});
            this.gdv.GridControl = this.gd;
            this.gdv.Name = "gdv";
            this.gdv.OptionsSelection.MultiSelect = true;
            this.gdv.OptionsView.ColumnAutoWidth = false;
            this.gdv.OptionsView.EnableAppearanceEvenRow = true;
            this.gdv.OptionsView.ShowGroupPanel = false;
            this.gdv.OptionsView.ShowIndicator = false;
            // 
            // Handler
            // 
            this.Handler.Caption = "Handler";
            this.Handler.FieldName = "Handler";
            this.Handler.Name = "Handler";
            this.Handler.Visible = true;
            this.Handler.VisibleIndex = 14;
            // 
            // Order
            // 
            this.Order.Caption = "Order";
            this.Order.FieldName = "Order";
            this.Order.Name = "Order";
            this.Order.Visible = true;
            this.Order.VisibleIndex = 1;
            // 
            // Row
            // 
            this.Row.Caption = "Row";
            this.Row.FieldName = "Row";
            this.Row.Name = "Row";
            this.Row.Visible = true;
            this.Row.VisibleIndex = 2;
            // 
            // Supplier
            // 
            this.Supplier.Caption = "Supplier";
            this.Supplier.FieldName = "Supplier";
            this.Supplier.Name = "Supplier";
            this.Supplier.Visible = true;
            this.Supplier.VisibleIndex = 3;
            // 
            // SupplName
            // 
            this.SupplName.Caption = "Suppl.Name";
            this.SupplName.FieldName = "SupplName";
            this.SupplName.Name = "SupplName";
            this.SupplName.Visible = true;
            this.SupplName.VisibleIndex = 4;
            // 
            // ItemID
            // 
            this.ItemID.Caption = "Item ID";
            this.ItemID.FieldName = "ItemID";
            this.ItemID.Name = "ItemID";
            this.ItemID.Visible = true;
            this.ItemID.VisibleIndex = 5;
            // 
            // ItemName
            // 
            this.ItemName.Caption = "Item Name";
            this.ItemName.FieldName = "ItemName";
            this.ItemName.Name = "ItemName";
            this.ItemName.Visible = true;
            this.ItemName.VisibleIndex = 6;
            // 
            // QtyOrd
            // 
            this.QtyOrd.Caption = "Qty Ord.";
            this.QtyOrd.FieldName = "QtyOrd";
            this.QtyOrd.Name = "QtyOrd";
            this.QtyOrd.Visible = true;
            this.QtyOrd.VisibleIndex = 7;
            // 
            // QtyLeft
            // 
            this.QtyLeft.Caption = "Qty Left";
            this.QtyLeft.FieldName = "QtyLeft";
            this.QtyLeft.Name = "QtyLeft";
            this.QtyLeft.Visible = true;
            this.QtyLeft.VisibleIndex = 8;
            // 
            // Unit
            // 
            this.Unit.Caption = "P.Unit";
            this.Unit.FieldName = "Unit";
            this.Unit.Name = "Unit";
            this.Unit.Visible = true;
            this.Unit.VisibleIndex = 9;
            // 
            // Status
            // 
            this.Status.Caption = "Status";
            this.Status.FieldName = "Status";
            this.Status.Name = "Status";
            this.Status.Visible = true;
            this.Status.VisibleIndex = 10;
            // 
            // DelDate
            // 
            this.DelDate.Caption = "Del.Date";
            this.DelDate.FieldName = "DelDate";
            this.DelDate.Name = "DelDate";
            this.DelDate.Visible = true;
            this.DelDate.VisibleIndex = 11;
            // 
            // ExtDelDate
            // 
            this.ExtDelDate.Caption = "Ext.Del.Date";
            this.ExtDelDate.FieldName = "ExtDelDate";
            this.ExtDelDate.Name = "ExtDelDate";
            this.ExtDelDate.Visible = true;
            this.ExtDelDate.VisibleIndex = 12;
            // 
            // POTransporttime
            // 
            this.POTransporttime.Caption = "PO Transport time";
            this.POTransporttime.FieldName = "POTransporttime";
            this.POTransporttime.Name = "POTransporttime";
            this.POTransporttime.Visible = true;
            this.POTransporttime.VisibleIndex = 13;
            // 
            // frmImportSTWK
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1176, 578);
            this.Controls.Add(this.groupControlImported);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frmImportSTWK";
            this.Text = "数据导入管理";
            this.Load += new System.EventHandler(this.frmImportSTWK_Load);
            ((System.ComponentModel.ISupportInitialize)(this.bs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlImported)).EndInit();
            this.groupControlImported.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gd)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem btnImportExcel;
        private DevExpress.XtraBars.BarButtonItem btnRefresh;
        private DevExpress.XtraBars.BarButtonItem btnSave;
        private DevExpress.XtraBars.BarButtonItem btnExport;
        private DevExpress.XtraBars.BarButtonItem btnReturn;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraEditors.GroupControl groupControlImported;
        private DevExpress.XtraGrid.GridControl gd;
        private DevExpress.XtraGrid.Views.Grid.GridView gdv;
        private DevExpress.XtraGrid.Columns.GridColumn Handler;
        private DevExpress.XtraGrid.Columns.GridColumn Order;
        private DevExpress.XtraGrid.Columns.GridColumn Row;
        private DevExpress.XtraGrid.Columns.GridColumn Supplier;
        private DevExpress.XtraGrid.Columns.GridColumn SupplName;
        private DevExpress.XtraGrid.Columns.GridColumn ItemID;
        private DevExpress.XtraGrid.Columns.GridColumn ItemName;
        private DevExpress.XtraGrid.Columns.GridColumn QtyOrd;
        private DevExpress.XtraGrid.Columns.GridColumn QtyLeft;
        private DevExpress.XtraGrid.Columns.GridColumn Unit;
        private DevExpress.XtraGrid.Columns.GridColumn Status;
        private DevExpress.XtraGrid.Columns.GridColumn DelDate;
        private DevExpress.XtraGrid.Columns.GridColumn ExtDelDate;
        private DevExpress.XtraGrid.Columns.GridColumn POTransporttime;
    }
}