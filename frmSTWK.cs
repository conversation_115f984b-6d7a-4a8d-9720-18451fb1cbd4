using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Collections;


namespace HIH.CRM.Import
{
    public partial class frmSTWK : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        public frmSTWK()
        {
            InitializeComponent();
            isLoadPerm = false;
            LoadMockData(); // 加载模拟数据
        }

        /// <summary>
        /// 创建并加载模拟数据到网格控件
        /// </summary>
        private void LoadMockData()
        {
            try
            {
                // 创建 DataTable
                DataTable dt = CreateMockDataTable();

                // 绑定数据到网格控件
                gd.DataSource = dt;
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError("加载模拟数据失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 创建模拟数据表
        /// </summary>
        /// <returns>包含模拟数据的 DataTable</returns>
        private DataTable CreateMockDataTable()
        {
            DataTable dt = new DataTable();

            // 添加列
            dt.Columns.Add("订单状态", typeof(string));
            dt.Columns.Add("订单剩余数量", typeof(decimal));
            dt.Columns.Add("订单号", typeof(string));
            dt.Columns.Add("行号", typeof(string));
            dt.Columns.Add("供应商代码", typeof(string));
            dt.Columns.Add("供应商名称", typeof(string));
            dt.Columns.Add("料号", typeof(string));
            dt.Columns.Add("料号名称", typeof(string));
            dt.Columns.Add("订单数量", typeof(decimal));
            dt.Columns.Add("剩余数量", typeof(decimal));
            dt.Columns.Add("单位", typeof(string));
            dt.Columns.Add("状态", typeof(string));
            dt.Columns.Add("交付日期", typeof(DateTime));
            dt.Columns.Add("延后交期", typeof(DateTime));
            dt.Columns.Add("运输时间", typeof(int));
            #region 模拟数据
            DataRow row1 = dt.NewRow();
            row1["订单状态"] =  "核销完成";
            row1["订单剩余数量"] = 0;
            row1["订单号"] = $"P007111";
            row1["行号"] = $"1";
            row1["供应商代码"] = $"E0000106";
            row1["供应商名称"] = $"GWB";
            row1["料号"] = $"BG00870699";
            row1["料号名称"] = $"DRIVE SHAFT";
            row1["订单数量"] = 1;
            row1["剩余数量"] = 1;
            row1["单位"] = "pcs";
            row1["状态"] = "Confirmed";
            row1["交付日期"] = "2025/7/25";
            row1["延后交期"] = "2025/5/16";
            row1["运输时间"] = "70";
            dt.Rows.Add(row1);


            DataRow row2 = dt.NewRow();
            row2["订单状态"] = "待核销";
            row2["订单剩余数量"] = 0;
            row2["订单号"] = $"P007111";
            row2["行号"] = $"2";
            row2["供应商代码"] = $"E0000106";
            row2["供应商名称"] = $"GWB";
            row2["料号"] = $"BG00870700";
            row2["料号名称"] = $"DRIVE SHAFT";
            row2["订单数量"] = 2;
            row2["剩余数量"] = 2;
            row2["单位"] = "pcs";
            row2["状态"] = "Confirmed";
            row2["交付日期"] = "2025/7/25";
            row2["延后交期"] = "2025/5/16";
            row2["运输时间"] = "70";
            dt.Rows.Add(row2);


            DataRow row3 = dt.NewRow();
            row3["订单状态"] = "未完成";
            row3["订单剩余数量"] = 0;
            row3["订单号"] = $"P007111";
            row3["行号"] = $"3";
            row3["供应商代码"] = $"E0000106";
            row3["供应商名称"] = $"GWB";
            row3["料号"] = $"BG00876540";
            row3["料号名称"] = $"DRIVE SHAFT";
            // 订单数量为空，应该显示红色文字
            row3["订单数量"] = 3;
            row3["剩余数量"] = 3;
            row3["单位"] = "pcs";
            row3["状态"] = "Pending";
            row3["交付日期"] = "2025/8/15";
            row3["延后交期"] = "2025/6/20";
            row3["运输时间"] = "60";
            dt.Rows.Add(row3);


            DataRow row4 = dt.NewRow();
            row4["订单状态"] = "核销完成";
            row4["订单剩余数量"] = 2;
            row4["订单号"] = $"P008358";
            row4["行号"] = $"1";
            row4["供应商代码"] = $"E0000031";
            row4["供应商名称"] = $"Wipro";
            row4["料号"] = $"56014134";
            row4["料号名称"] = $"HYDRAULIC CYLINDER, DOUBLE-ACTING";
            row4["订单数量"] = 10;
            row4["剩余数量"] = 8;
            row4["单位"] = "pcs";
            row4["状态"] = "Confirmed";
            row4["交付日期"] = "2025/7/25";
            row4["延后交期"] = "2025/5/16";
            row4["运输时间"] = "70";
            dt.Rows.Add(row4);


            // 添加一个订单数量为0的测试行，应该显示红色文字
            DataRow row5 = dt.NewRow();
            row5["订单状态"] = "待核销";
            row5["订单剩余数量"] = 0;
            row5["订单号"] = $"P008359";
            row5["行号"] = $"1";
            row5["供应商代码"] = $"E0000032";
            row5["供应商名称"] = $"TestSupplier";
            row5["料号"] = $"TEST001";
            row5["料号名称"] = $"TEST ITEM";
            row5["订单数量"] = 2; // 订单数量为0，应该显示红色文字
            row5["剩余数量"] = 2;
            row5["单位"] = "pcs";
            row5["状态"] = "Pending";
            row5["交付日期"] = "2025/9/01";
            row5["延后交期"] = "2025/7/01";
            row5["运输时间"] = "30";
            dt.Rows.Add(row5);

            DataRow row6 = dt.NewRow();
            row6["订单状态"] = "未完成";
            row6["订单剩余数量"] = 2;
            row6["订单号"] = $"P008599";
            row6["行号"] = $"1";
            row6["供应商代码"] = $"E0000032";
            row6["供应商名称"] = $"TestSupplier";
            row6["料号"] = $"TEST001";
            row6["料号名称"] = $"TEST ITEM";
            dt.Rows.Add(row6);

            #endregion

            return dt;
        }

        private void gdv_MouseDown(object sender, MouseEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hInfo = gdv.CalcHitInfo(new Point(e.X, e.Y));
                if (e.Button == MouseButtons.Left && e.Clicks == 2)//判断是否左键双击
                {
                    //判断光标是否在行范围内
                    if (hInfo.InRow)
                    {
                        //获取选择当前行的数据
                        int rowHandle = hInfo.RowHandle;
                        ArrayList formParam = new ArrayList()
                        {
                            gdv.GetRowCellValue(rowHandle, "订单状态"),
                            gdv.GetRowCellValue(rowHandle, "订单剩余数量"),
                            gdv.GetRowCellValue(rowHandle, "订单号"),
                            gdv.GetRowCellValue(rowHandle, "行号"),
                            gdv.GetRowCellValue(rowHandle, "供应商代码"),
                            gdv.GetRowCellValue(rowHandle, "供应商名称"),
                            gdv.GetRowCellValue(rowHandle, "料号"),
                            gdv.GetRowCellValue(rowHandle, "料号名称"),
                            gdv.GetRowCellValue(rowHandle, "订单数量"),
                            gdv.GetRowCellValue(rowHandle, "剩余数量"),
                            gdv.GetRowCellValue(rowHandle, "单位"),
                            gdv.GetRowCellValue(rowHandle, "状态"),
                            gdv.GetRowCellValue(rowHandle, "交付日期"),
                            gdv.GetRowCellValue(rowHandle, "延后交期"),
                            gdv.GetRowCellValue(rowHandle, "运输时间")
                        };
                        ICF.ISD.SubSingleFormShow("HIH.CRM.Import.dll",
                           "HIH.CRM.Import.frmSTWKEdit",
                           "山特维克明细", formParam);
                    }
                }
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 设置网格行样式，当订单数量为空或0时显示红色文字
        /// </summary>
        private void gdv_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.Views.Grid.GridView view = sender as DevExpress.XtraGrid.Views.Grid.GridView;
                if (view == null) return;

                // 获取订单数量的值
                object orderQty = view.GetRowCellValue(e.RowHandle, "订单数量");

                object orderQty2 = view.GetRowCellValue(e.RowHandle, "订单剩余数量");

                // 检查订单数量是否为空或0
                if ((orderQty == null || orderQty == DBNull.Value)&& (orderQty2 != null || orderQty2 != DBNull.Value))
                {
                    // 设置整行文字为红色
                    e.Appearance.ForeColor = Color.Red;
                }
                else if (decimal.TryParse(orderQty.ToString(), out decimal qty) && qty == 0 && decimal.TryParse(orderQty2.ToString(), out decimal qty2) && qty2 != 0)
                {
                    // 设置整行文字为红色
                    e.Appearance.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                // 忽略样式设置错误，避免影响界面显示
            }
        }

        private void btnImport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                ArrayList al = new ArrayList();

                ICF.ISD.SubFormShowModal("HIH.CRM.Import.dll",
                                         "HIH.CRM.Import.frmImportSTWK",
                                         "导入",
                                         al
                                         );
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void btnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                LoadMockData(); // 重新加载模拟数据
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError("刷新数据失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取查询条件
                string orderNo = textEdit1.Text.Trim();
                string rowNo = txtRow.Text.Trim();
                string orderStatus = cbbOrderStatus.Text.Trim();

                // 创建过滤后的数据
                DataTable filteredData = FilterData(orderNo, rowNo, orderStatus);

                // 绑定过滤后的数据
                gd.DataSource = filteredData;
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError("查询失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 根据条件过滤数据
        /// </summary>
        private DataTable FilterData(string orderNo, string rowNo, string orderStatus)
        {
            DataTable originalData = CreateMockDataTable();
            DataTable filteredData = originalData.Clone(); // 复制结构

            foreach (DataRow row in originalData.Rows)
            {
                bool match = true;

                // 订单号过滤
                if (!string.IsNullOrEmpty(orderNo))
                {
                    if (!row["订单号"].ToString().Contains(orderNo))
                        match = false;
                }

                // 行号过滤
                if (!string.IsNullOrEmpty(rowNo))
                {
                    if (!row["行号"].ToString().Contains(rowNo))
                        match = false;
                }

                // 订单状态过滤
                if (!string.IsNullOrEmpty(orderStatus))
                {
                    if (row["订单状态"].ToString() != orderStatus)
                        match = false;
                }

                if (match)
                {
                    filteredData.ImportRow(row);
                }
            }

            return filteredData;
        }
    }
}
